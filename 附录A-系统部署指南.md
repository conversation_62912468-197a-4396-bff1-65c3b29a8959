# 附录A：系统部署指南

## 1. 环境要求

### 1.1 硬件要求

**最低配置：**
- CPU：2核心 2.0GHz
- 内存：4GB RAM
- 存储：20GB 可用空间
- 网络：稳定的互联网连接

**推荐配置：**
- CPU：4核心 2.5GHz
- 内存：8GB RAM
- 存储：50GB 可用空间（SSD推荐）
- 网络：带宽≥10Mbps

### 1.2 软件要求

**操作系统：**
- Windows 10/11
- macOS 10.15+
- Ubuntu 20.04+ / CentOS 8+

**必需软件：**
- Python 3.8+
- Node.js 16+
- MySQL 8.0+
- Git

## 2. 开发环境搭建

### 2.1 Python环境安装

**Windows系统：**
```bash
# 下载并安装Python 3.8+
# 访问 https://www.python.org/downloads/
# 安装时勾选"Add Python to PATH"

# 验证安装
python --version
pip --version
```

**Linux/macOS系统：**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip

# macOS (使用Homebrew)
brew install python@3.9

# 验证安装
python3 --version
pip3 --version
```

### 2.2 Node.js环境安装

**Windows系统：**
```bash
# 下载并安装Node.js 16+
# 访问 https://nodejs.org/
# 选择LTS版本下载安装

# 验证安装
node --version
npm --version
```

**Linux/macOS系统：**
```bash
# 使用NodeSource仓库（推荐）
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用包管理器
# Ubuntu/Debian
sudo apt install nodejs npm

# macOS
brew install node

# 验证安装
node --version
npm --version
```

### 2.3 MySQL数据库安装

**Windows系统：**
```bash
# 下载MySQL Installer
# 访问 https://dev.mysql.com/downloads/installer/
# 选择"mysql-installer-community"版本

# 安装配置
# 1. 选择"Developer Default"安装类型
# 2. 设置root密码
# 3. 创建数据库用户（可选）
```

**Linux系统：**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# 安全配置
sudo mysql_secure_installation

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

**macOS系统：**
```bash
# 使用Homebrew
brew install mysql

# 启动服务
brew services start mysql

# 安全配置
mysql_secure_installation
```

### 2.4 Git版本控制

**安装Git：**
```bash
# Windows: 下载Git for Windows
# https://git-scm.com/download/win

# Linux
sudo apt install git  # Ubuntu/Debian
sudo yum install git  # CentOS/RHEL

# macOS
brew install git
```

## 3. 项目依赖安装

### 3.1 克隆项目代码

```bash
# 克隆项目仓库
git clone <项目仓库地址>
cd teaching_agent_system

# 查看项目结构
ls -la
```

### 3.2 后端依赖安装

```bash
# 进入后端目录
cd teaching_agent_backend

# 创建Python虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate

# 升级pip
python -m pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

**requirements.txt 主要依赖：**
```txt
fastapi==0.103.1
uvicorn==0.23.2
pydantic==2.3.0
sqlalchemy==2.0.20
pymysql==1.1.0
chromadb==0.4.13
langchain-google-genai
python-dotenv
google-generativeai==0.6.0
python-jose==3.3.0
passlib==1.7.4
python-multipart==0.0.6
```

### 3.3 前端依赖安装

```bash
# 进入前端目录
cd ../teaching_agent_frontend

# 安装依赖
npm install

# 或使用yarn（如果已安装）
yarn install
```

**package.json 主要依赖：**
```json
{
  "dependencies": {
    "vue": "^3.2.13",
    "vue-router": "^4.0.3",
    "pinia": "^3.0.3",
    "axios": "^1.10.0",
    "marked": "^15.0.12"
  }
}
```

## 4. 数据库配置

### 4.1 创建数据库

```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE teaching_agent CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建数据库用户（可选）
CREATE USER 'teaching_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON teaching_agent.* TO 'teaching_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出MySQL
EXIT;
```

### 4.2 数据库表初始化

```bash
# 进入后端目录
cd teaching_agent_backend

# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 运行数据库初始化脚本
python init_database.py
```

## 5. 环境变量配置

### 5.1 后端环境变量

```bash
# 进入后端目录
cd teaching_agent_backend

# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env  # Linux/macOS
# 或使用其他编辑器
```

**.env 配置示例：**
```env
# Google API配置
GOOGLE_API_KEY=your_google_api_key_here

# 数据库配置
DB_USER=teaching_user
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=teaching_agent

# 向量数据库配置
CHROMA_DB_PATH=./chroma_db_store

# 安全配置
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 服务器配置
PORT=8000
```

### 5.2 获取Google API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录Google账户
3. 点击"Create API Key"
4. 复制生成的API密钥
5. 将密钥填入`.env`文件的`GOOGLE_API_KEY`字段

### 5.3 前端环境配置

```bash
# 进入前端目录
cd teaching_agent_frontend

# 创建环境配置文件（如需要）
touch .env.local
```

**.env.local 配置示例：**
```env
# API基础URL
VUE_APP_API_BASE_URL=http://localhost:8000

# 其他配置
VUE_APP_TITLE=教学培训智能代理系统
```

## 6. 服务启动

### 6.1 启动后端服务

```bash
# 进入后端目录并激活虚拟环境
cd teaching_agent_backend
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 启动FastAPI服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 或使用Python直接运行
python main.py
```

**启动成功标志：**
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [xxxxx] using StatReload
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

### 6.2 启动前端服务

```bash
# 新开终端，进入前端目录
cd teaching_agent_frontend

# 启动开发服务器
npm run dev

# 或使用yarn
yarn dev
```

**启动成功标志：**
```
  App running at:
  - Local:   http://localhost:8080/
  - Network: http://192.168.x.x:8080/
```

## 7. 服务验证

### 7.1 后端API验证

```bash
# 健康检查
curl http://localhost:8000/api/health

# 预期响应
{"status":"ok","timestamp":**********.123}

# 访问API文档
# 浏览器打开: http://localhost:8000/docs
```

### 7.2 前端界面验证

```bash
# 访问前端界面
# 浏览器打开: http://localhost:8080

# 检查控制台是否有错误
# 按F12打开开发者工具查看Console
```

### 7.3 数据库连接验证

```bash
# 进入后端目录
cd teaching_agent_backend
source venv/bin/activate

# 运行数据库连接测试
python -c "
from models.database import engine
try:
    with engine.connect() as conn:
        print('数据库连接成功!')
except Exception as e:
    print(f'数据库连接失败: {e}')
"
```

## 8. 常见问题排查

### 8.1 Python依赖问题

**问题：** `pip install` 失败
```bash
# 解决方案
# 1. 升级pip
python -m pip install --upgrade pip

# 2. 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 3. 单独安装问题包
pip install package_name --no-cache-dir
```

### 8.2 Node.js依赖问题

**问题：** `npm install` 失败
```bash
# 解决方案
# 1. 清除npm缓存
npm cache clean --force

# 2. 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 3. 使用国内镜像源
npm install --registry https://registry.npm.taobao.org
```

### 8.3 数据库连接问题

**问题：** 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# 检查端口占用
netstat -an | grep 3306

# 检查用户权限
mysql -u root -p
SHOW GRANTS FOR 'teaching_user'@'localhost';
```

### 8.4 端口占用问题

**问题：** 端口被占用
```bash
# 查看端口占用
# Windows
netstat -ano | findstr :8000

# Linux/macOS
lsof -i :8000

# 终止占用进程
# Windows
taskkill /PID <进程ID> /F

# Linux/macOS
kill -9 <进程ID>
```



## 10. 系统监控

### 10.1 日志配置

**后端日志：**
- 日志文件：`teaching_agent_backend.log`
- 日志级别：INFO
- 轮转策略：按大小轮转

**前端日志：**
- 浏览器控制台
- 错误监控服务（可选）

### 10.2 性能监控

```bash
# 系统资源监控
htop  # Linux
top   # 通用

# 数据库性能监控
SHOW PROCESSLIST;  # MySQL
SHOW STATUS LIKE 'Threads_connected';
```

---

**部署完成检查清单：**

- [ ] Python环境安装完成
- [ ] Node.js环境安装完成  
- [ ] MySQL数据库安装配置完成
- [ ] 项目代码克隆完成
- [ ] 后端依赖安装完成
- [ ] 前端依赖安装完成
- [ ] 数据库创建和初始化完成
- [ ] 环境变量配置完成
- [ ] Google API密钥配置完成
- [ ] 后端服务启动成功
- [ ] 前端服务启动成功
- [ ] API接口验证通过
- [ ] 前端界面访问正常
- [ ] 数据库连接验证通过

完成以上所有步骤后，系统即可正常运行。如遇到问题，请参考常见问题排查部分或查看系统日志进行诊断。
