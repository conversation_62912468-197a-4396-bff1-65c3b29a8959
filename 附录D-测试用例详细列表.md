# 附录D：测试用例详细列表

## 1. 测试概述

### 1.1 测试策略

本系统采用多层次测试策略：
- **单元测试**：测试独立的函数和方法
- **集成测试**：测试模块间的交互
- **系统测试**：测试完整的业务流程
- **用户验收测试**：验证用户需求满足度

### 1.2 测试环境

- **测试数据库**：独立的MySQL测试实例
- **测试向量库**：内存模式的ChromaDB
- **模拟服务**：Mock的外部API服务
- **测试数据**：预设的测试用户和题目数据

## 2. 用户认证功能测试

### 2.1 用户注册测试

**测试用例TC_AUTH_001：正常用户注册**
- **测试目标**：验证用户能够成功注册账户
- **前置条件**：系统正常运行，数据库连接正常
- **测试步骤**：
  1. 访问注册页面
  2. 输入有效的用户名、邮箱、密码
  3. 选择用户角色（学生）
  4. 点击注册按钮
- **测试数据**：
  ```json
  {
    "username": "test_student_001",
    "email": "<EMAIL>",
    "password": "Test123456",
    "full_name": "测试学生001",
    "role": "student"
  }
  ```
- **期望结果**：
  - 注册成功，返回用户信息
  - 密码被正确哈希存储
  - 用户状态为active
  - 自动跳转到登录页面

**测试用例TC_AUTH_002：重复用户名注册**
- **测试目标**：验证系统拒绝重复用户名注册
- **前置条件**：已存在用户名为"existing_user"的用户
- **测试步骤**：
  1. 尝试注册用户名为"existing_user"的新用户
  2. 提交注册表单
- **期望结果**：
  - 注册失败
  - 返回错误信息："用户名已存在"
  - 状态码：400

**测试用例TC_AUTH_003：无效邮箱格式注册**
- **测试目标**：验证邮箱格式验证功能
- **测试数据**：
  ```json
  {
    "username": "test_user",
    "email": "invalid_email_format",
    "password": "Test123456"
  }
  ```
- **期望结果**：
  - 注册失败
  - 返回错误信息："邮箱格式不正确"

### 2.2 用户登录测试

**测试用例TC_AUTH_004：正常用户登录**
- **测试目标**：验证用户能够成功登录
- **前置条件**：存在有效的测试用户账户
- **测试步骤**：
  1. 访问登录页面
  2. 输入正确的用户名和密码
  3. 点击登录按钮
- **测试数据**：
  ```json
  {
    "username": "test_student_001",
    "password": "Test123456"
  }
  ```
- **期望结果**：
  - 登录成功
  - 返回JWT访问令牌
  - 令牌有效期为30分钟
  - 更新最后登录时间

**测试用例TC_AUTH_005：错误密码登录**
- **测试目标**：验证错误密码被拒绝
- **测试数据**：
  ```json
  {
    "username": "test_student_001",
    "password": "WrongPassword"
  }
  ```
- **期望结果**：
  - 登录失败
  - 返回错误信息："用户名或密码错误"
  - 状态码：401

**测试用例TC_AUTH_006：令牌过期验证**
- **测试目标**：验证过期令牌被正确处理
- **前置条件**：拥有已过期的JWT令牌
- **测试步骤**：
  1. 使用过期令牌访问受保护的API
- **期望结果**：
  - 访问被拒绝
  - 返回错误信息："令牌已过期"
  - 状态码：401

## 3. 智能问答功能测试

### 3.1 RAG问答测试

**测试用例TC_RAG_001：正常问答查询**
- **测试目标**：验证RAG问答功能正常工作
- **前置条件**：
  - 知识库已导入测试文档
  - 向量数据库包含相关内容
- **测试步骤**：
  1. 发送问答请求
  2. 等待系统响应
- **测试数据**：
  ```json
  {
    "query": "什么是操作系统？",
    "n_retrieved_docs": 3
  }
  ```
- **期望结果**：
  - 返回相关的答案
  - 包含检索到的文档片段
  - 响应时间 < 3秒
  - 答案与查询相关性 > 80%

**测试用例TC_RAG_002：空查询处理**
- **测试目标**：验证空查询的处理
- **测试数据**：
  ```json
  {
    "query": "",
    "n_retrieved_docs": 3
  }
  ```
- **期望结果**：
  - 返回错误信息："查询不能为空"
  - 状态码：400

**测试用例TC_RAG_003：超长查询处理**
- **测试目标**：验证超长查询的处理
- **测试数据**：包含2000字符的查询文本
- **期望结果**：
  - 查询被截断到合理长度
  - 或返回错误信息："查询过长"

### 3.2 流式输出测试

**测试用例TC_STREAM_001：流式问答输出**
- **测试目标**：验证流式输出功能
- **前置条件**：客户端支持Server-Sent Events
- **测试步骤**：
  1. 发送流式问答请求
  2. 监听SSE事件流
  3. 记录接收到的数据块
- **期望结果**：
  - 接收到start事件
  - 接收到多个content事件
  - 接收到end事件
  - 内容完整且有序

## 4. 题目生成功能测试

### 4.1 题目生成测试

**测试用例TC_QUESTION_001：选择题生成**
- **测试目标**：验证选择题生成功能
- **前置条件**：
  - 用户具有教师权限
  - 知识点数据存在
- **测试数据**：
  ```json
  {
    "knowledge_point_ids": [1, 2],
    "question_types": ["multiple_choice"],
    "difficulty_level": "medium",
    "question_count": 5
  }
  ```
- **期望结果**：
  - 生成5道选择题
  - 每题包含4个选项
  - 题目与知识点相关
  - 包含正确答案和解析

**测试用例TC_QUESTION_002：填空题生成**
- **测试目标**：验证填空题生成功能
- **测试数据**：
  ```json
  {
    "knowledge_point_ids": [3],
    "question_types": ["fill_in_the_blank"],
    "difficulty_level": "easy",
    "question_count": 3
  }
  ```
- **期望结果**：
  - 生成3道填空题
  - 题目包含空白位置标记
  - 提供标准答案
  - 支持多个正确答案

**测试用例TC_QUESTION_003：简答题生成**
- **测试目标**：验证简答题生成功能
- **测试数据**：
  ```json
  {
    "knowledge_point_ids": [4, 5],
    "question_types": ["short_answer"],
    "difficulty_level": "hard",
    "question_count": 2
  }
  ```
- **期望结果**：
  - 生成2道简答题
  - 题目开放性适中
  - 提供参考答案
  - 包含评分要点

### 4.2 题目质量验证

**测试用例TC_QUALITY_001：题目重复性检查**
- **测试目标**：验证生成的题目不重复
- **测试步骤**：
  1. 连续生成多批题目
  2. 检查题目文本相似度
- **期望结果**：
  - 题目文本相似度 < 80%
  - 不存在完全相同的题目

**测试用例TC_QUALITY_002：答案准确性验证**
- **测试目标**：验证生成题目的答案正确性
- **测试步骤**：
  1. 人工审核生成的题目
  2. 验证答案的正确性
- **期望结果**：
  - 答案准确率 > 95%
  - 解析内容合理

## 5. 智能评测功能测试

### 5.1 客观题评测测试

**测试用例TC_GRADE_001：选择题自动批改**
- **测试目标**：验证选择题自动批改功能
- **前置条件**：存在选择题和学生答案
- **测试数据**：
  ```json
  {
    "question_id": 1,
    "student_answer": "A",
    "correct_answer": "A"
  }
  ```
- **期望结果**：
  - 评测结果：正确
  - 自动批改标记：true
  - 响应时间 < 100ms

**测试用例TC_GRADE_002：填空题批改**
- **测试目标**：验证填空题批改功能
- **测试数据**：
  ```json
  {
    "question_id": 2,
    "student_answer": ["进程", "线程"],
    "correct_answer": ["进程", "线程"]
  }
  ```
- **期望结果**：
  - 支持多个答案验证
  - 支持大小写不敏感匹配
  - 支持同义词匹配

### 5.2 主观题AI评测测试

**测试用例TC_AI_GRADE_001：简答题AI评分**
- **测试目标**：验证简答题AI评分功能
- **前置条件**：
  - AI评测服务可用
  - 存在简答题和参考答案
- **测试数据**：
  ```json
  {
    "question_id": 3,
    "question_text": "请解释操作系统中进程和线程的区别。",
    "student_answer": "进程是程序的执行实例，线程是进程内的执行单元。进程有独立的内存空间，线程共享进程的内存空间。",
    "reference_answer": "进程是程序在执行时的一个实例，拥有独立的内存空间；线程是进程内的执行单元，多个线程共享同一进程的内存空间。"
  }
  ```
- **期望结果**：
  - 返回0-100分的评分
  - 提供详细的评分反馈
  - 给出改进建议
  - 评分时间 < 5秒

**测试用例TC_AI_GRADE_002：无效答案处理**
- **测试目标**：验证无效答案的处理
- **测试数据**：
  ```json
  {
    "question_id": 3,
    "student_answer": "不知道"
  }
  ```
- **期望结果**：
  - 评分：0-10分
  - 反馈："答案无效，请重新作答"

## 6. 学习分析功能测试

### 6.1 学习数据分析测试

**测试用例TC_ANALYTICS_001：学生学习分析**
- **测试目标**：验证学生学习数据分析功能
- **前置条件**：
  - 学生有足够的练习记录
  - 数据时间跨度 > 7天
- **测试步骤**：
  1. 请求学生学习分析数据
  2. 验证返回的分析结果
- **期望结果**：
  - 包含整体表现统计
  - 包含知识点掌握度分析
  - 包含学习模式识别
  - 包含薄弱环节识别

**测试用例TC_ANALYTICS_002：知识点掌握度计算**
- **测试目标**：验证知识点掌握度计算准确性
- **测试数据**：
  - 知识点A：10次练习，8次正确
  - 知识点B：5次练习，2次正确
- **期望结果**：
  - 知识点A掌握度：80%
  - 知识点B掌握度：40%
  - 计算结果准确

### 6.2 学习趋势分析测试

**测试用例TC_TREND_001：学习进步趋势**
- **测试目标**：验证学习进步趋势分析
- **前置条件**：学生有时间序列的练习数据
- **期望结果**：
  - 识别出进步或退步趋势
  - 计算进步率
  - 提供趋势图数据

## 7. 性能测试

### 7.1 负载测试

**测试用例TC_LOAD_001：并发用户测试**
- **测试目标**：验证系统并发处理能力
- **测试配置**：
  - 并发用户数：50
  - 测试时长：10分钟
  - 操作类型：混合（登录、问答、练习）
- **期望结果**：
  - 平均响应时间 < 2秒
  - 错误率 < 1%
  - 系统稳定运行

**测试用例TC_LOAD_002：数据库压力测试**
- **测试目标**：验证数据库性能
- **测试配置**：
  - 并发查询数：100
  - 查询类型：复杂联表查询
- **期望结果**：
  - 查询响应时间 < 500ms
  - 数据库连接池正常
  - 无死锁发生

### 7.2 压力测试

**测试用例TC_STRESS_001：极限并发测试**
- **测试目标**：找到系统性能极限
- **测试方法**：
  1. 逐步增加并发用户数
  2. 监控系统响应时间和错误率
  3. 找到性能拐点
- **期望结果**：
  - 确定最大并发用户数
  - 系统优雅降级
  - 无数据丢失

## 8. 安全测试

### 8.1 认证安全测试

**测试用例TC_SEC_001：SQL注入防护**
- **测试目标**：验证SQL注入防护
- **测试数据**：
  ```json
  {
    "username": "admin'; DROP TABLE users; --",
    "password": "password"
  }
  ```
- **期望结果**：
  - 登录失败
  - 数据库表未被删除
  - 记录安全日志

**测试用例TC_SEC_002：XSS攻击防护**
- **测试目标**：验证XSS攻击防护
- **测试数据**：
  ```json
  {
    "query": "<script>alert('XSS')</script>"
  }
  ```
- **期望结果**：
  - 脚本被转义或过滤
  - 不执行恶意代码

### 8.2 权限控制测试

**测试用例TC_PERM_001：角色权限验证**
- **测试目标**：验证角色权限控制
- **测试步骤**：
  1. 学生用户尝试访问教师功能
  2. 验证访问被拒绝
- **期望结果**：
  - 访问被拒绝
  - 返回403状态码
  - 错误信息："权限不足"

## 9. 用户界面测试

### 9.1 功能界面测试

**测试用例TC_UI_001：登录界面测试**
- **测试目标**：验证登录界面功能
- **测试步骤**：
  1. 检查页面元素完整性
  2. 测试表单验证
  3. 测试登录流程
- **期望结果**：
  - 所有元素正常显示
  - 表单验证正确
  - 登录成功后正确跳转

**测试用例TC_UI_002：响应式设计测试**
- **测试目标**：验证响应式设计
- **测试环境**：
  - 桌面端：1920x1080
  - 平板端：768x1024
  - 手机端：375x667
- **期望结果**：
  - 各尺寸下界面正常显示
  - 功能完整可用
  - 用户体验良好

### 9.2 兼容性测试

**测试用例TC_COMPAT_001：浏览器兼容性**
- **测试目标**：验证浏览器兼容性
- **测试环境**：
  - Chrome 90+
  - Firefox 88+
  - Safari 14+
  - Edge 90+
- **期望结果**：
  - 所有浏览器功能正常
  - 界面显示一致
  - 性能表现良好

## 10. 测试数据管理

### 10.1 测试数据准备

**用户测试数据：**
```sql
INSERT INTO users (username, email, hashed_password, role) VALUES
('test_student', '<EMAIL>', '$2b$12$...', 'student'),
('test_teacher', '<EMAIL>', '$2b$12$...', 'teacher'),
('test_admin', '<EMAIL>', '$2b$12$...', 'admin');
```

**题目测试数据：**
```sql
INSERT INTO questions (question_text, question_type, answer, difficulty_level) VALUES
('操作系统的主要功能包括？', 'multiple_choice', 'D', 'medium'),
('进程和线程的区别是什么？', 'short_answer', '参考答案...', 'hard');
```

### 10.2 测试环境清理

**清理脚本：**
```sql
-- 清理测试数据
DELETE FROM student_answers WHERE practice_record_id IN (
    SELECT id FROM student_practice_records WHERE user_id IN (
        SELECT id FROM users WHERE username LIKE 'test_%'
    )
);

DELETE FROM student_practice_records WHERE user_id IN (
    SELECT id FROM users WHERE username LIKE 'test_%'
);

DELETE FROM users WHERE username LIKE 'test_%';
```

---

**测试执行计划：**

1. **第一阶段**：单元测试和集成测试（开发阶段）
2. **第二阶段**：系统测试和性能测试（测试阶段）
3. **第三阶段**：安全测试和兼容性测试（发布前）
4. **第四阶段**：用户验收测试（发布后）

**测试覆盖率目标：**
- 代码覆盖率：> 80%
- 功能覆盖率：> 95%
- 接口覆盖率：100%

**测试通过标准：**
- 所有关键功能测试通过
- 性能指标达到要求
- 安全测试无高危漏洞
- 用户验收测试满意度 > 90%
