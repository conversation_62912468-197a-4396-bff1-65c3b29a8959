# 附录C：数据库设计详细说明

## 1. 数据库架构概述

### 1.1 混合存储架构

本系统采用混合存储架构，结合关系型数据库和向量数据库的优势：

- **MySQL 8.0**：存储结构化业务数据
- **ChromaDB**：存储文档向量和元数据

### 1.2 设计原则

- **数据一致性**：确保关系数据的ACID特性
- **性能优化**：合理设计索引和查询优化
- **可扩展性**：支持水平和垂直扩展
- **数据安全**：敏感数据加密存储

## 2. MySQL关系数据库设计

### 2.1 用户表 (users)

**表结构：**
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱地址',
    full_name VARCHAR(100) COMMENT '真实姓名',
    hashed_password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('student', 'teacher', 'admin') DEFAULT 'student' COMMENT '用户角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '账户状态',
    phone VARCHAR(20) COMMENT '手机号码',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

**字段说明：**
- `id`: 主键，自增整数
- `username`: 用户名，唯一约束，用于登录
- `email`: 邮箱地址，唯一约束
- `hashed_password`: 使用bcrypt加密的密码哈希
- `role`: 用户角色，支持学生、教师、管理员三种角色
- `status`: 账户状态，支持激活、未激活、暂停三种状态

### 2.2 知识点表 (knowledge_points)

**表结构：**
```sql
CREATE TABLE knowledge_points (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '知识点ID',
    parent_id INT NULL COMMENT '父级知识点ID',
    name VARCHAR(255) NOT NULL COMMENT '知识点名称',
    description TEXT COMMENT '知识点描述',
    content_reference_id VARCHAR(255) COMMENT 'ChromaDB文档引用ID',
    knowledge_base_metadata_id INT COMMENT '关联的知识库元数据ID',
    level INT DEFAULT 1 COMMENT '层级深度：1=章节，2=知识点，3=子知识点',
    order_index INT DEFAULT 0 COMMENT '同级别内的排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (parent_id) REFERENCES knowledge_points(id) ON DELETE SET NULL,
    FOREIGN KEY (knowledge_base_metadata_id) REFERENCES knowledge_base_metadata(id) ON DELETE SET NULL,
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_order_index (order_index),
    INDEX idx_is_active (is_active),
    INDEX idx_knowledge_base_metadata_id (knowledge_base_metadata_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识点表';
```

**层级关系设计：**
- 支持无限层级的树形结构
- `parent_id`为NULL表示根节点
- `level`字段便于查询特定层级的知识点
- `order_index`用于同级排序

### 2.3 题目表 (questions)

**表结构：**
```sql
CREATE TABLE questions (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '题目ID',
    question_text TEXT NOT NULL COMMENT '题干内容',
    question_type ENUM('multiple_choice', 'fill_in_the_blank', 'short_answer') NOT NULL COMMENT '题型',
    options JSON COMMENT '选项（JSON格式，仅选择题使用）',
    answer JSON NOT NULL COMMENT '答案（JSON格式，支持多种题型）',
    explanation TEXT COMMENT '题目解析',
    knowledge_point_ids JSON COMMENT '关联的知识点ID列表',
    associated_knowledge_snippet TEXT COMMENT '关联的知识点原文片段',
    difficulty_level ENUM('easy', 'medium', 'hard') DEFAULT 'medium' COMMENT '难度级别',
    created_by_user_id INT COMMENT '创建者用户ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (created_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_question_type (question_type),
    INDEX idx_difficulty_level (difficulty_level),
    INDEX idx_created_by_user_id (created_by_user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_question_text (question_text)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目表';
```

**JSON字段设计：**

**选择题options格式：**
```json
["A. 选项1", "B. 选项2", "C. 选项3", "D. 选项4"]
```

**不同题型answer格式：**
```json
// 选择题
"A"

// 填空题
["答案1", "答案2"]

// 简答题
"参考答案内容"
```

### 2.4 学生练习记录表 (student_practice_records)

**表结构：**
```sql
CREATE TABLE student_practice_records (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '练习记录ID',
    user_id INT NOT NULL COMMENT '学生用户ID',
    session_id VARCHAR(255) NOT NULL UNIQUE COMMENT '练习会话ID',
    practice_type ENUM('knowledge_point', 'test_set') NOT NULL COMMENT '练习类型',
    knowledge_point_ids JSON COMMENT '关联的知识点ID列表',
    question_ids JSON COMMENT '练习中的题目ID列表',
    test_set_id INT COMMENT '套卷ID（如果是套卷练习）',
    
    -- 练习统计
    total_questions INT NOT NULL COMMENT '总题目数',
    completed_questions INT DEFAULT 0 COMMENT '已完成题目数',
    correct_count INT DEFAULT 0 COMMENT '正确题目数',
    score DECIMAL(5,2) DEFAULT 0 COMMENT '总分',
    
    -- 时间记录
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_seconds INT COMMENT '练习时长（秒）',
    
    -- 状态管理
    status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'in_progress' COMMENT '练习状态',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (test_set_id) REFERENCES test_sets(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_practice_type (practice_type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生练习记录表';
```

### 2.5 学生答题记录表 (student_answers)

**表结构：**
```sql
CREATE TABLE student_answers (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '答题记录ID',
    practice_record_id INT NOT NULL COMMENT '练习记录ID',
    question_id INT NOT NULL COMMENT '题目ID',
    question_type ENUM('multiple_choice', 'fill_in_the_blank', 'short_answer') NOT NULL COMMENT '题型',
    student_answer TEXT NOT NULL COMMENT '学生答案',
    
    -- 评测结果
    is_correct BOOLEAN COMMENT '是否正确（客观题）',
    ai_score DECIMAL(5,2) COMMENT 'AI评分（主观题，0-100）',
    ai_feedback TEXT COMMENT 'AI评价反馈',
    improvement_suggestions TEXT COMMENT '改进建议',
    generated_reference_answer TEXT COMMENT 'AI生成的参考答案',
    
    -- 评测元信息
    auto_graded BOOLEAN DEFAULT FALSE COMMENT '是否自动批改',
    ai_model_used VARCHAR(100) COMMENT '使用的AI模型',
    grading_time TIMESTAMP COMMENT '批改时间',
    
    -- 时间记录
    answer_start_time TIMESTAMP COMMENT '开始答题时间',
    answer_submit_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交答案时间',
    answer_duration_seconds INT COMMENT '答题时长（秒）',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (practice_record_id) REFERENCES student_practice_records(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    
    UNIQUE KEY uk_practice_question (practice_record_id, question_id),
    INDEX idx_practice_record_id (practice_record_id),
    INDEX idx_question_id (question_id),
    INDEX idx_question_type (question_type),
    INDEX idx_is_correct (is_correct),
    INDEX idx_ai_score (ai_score),
    INDEX idx_answer_submit_time (answer_submit_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生答题记录表';
```

### 2.6 知识库元数据表 (knowledge_base_metadata)

**表结构：**
```sql
CREATE TABLE knowledge_base_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '知识库元数据ID',
    document_name VARCHAR(255) NOT NULL COMMENT '文档名称',
    file_path TEXT COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小（字节）',
    chunks_count INT COMMENT '文档块数量',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    status ENUM('processing', 'completed', 'failed') DEFAULT 'processing' COMMENT '处理状态',
    
    INDEX idx_document_name (document_name),
    INDEX idx_status (status),
    INDEX idx_upload_time (upload_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库元数据表';
```

### 2.7 问答历史表 (qa_history)

**表结构：**
```sql
CREATE TABLE qa_history (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '问答历史ID',
    user_id INT COMMENT '用户ID（可为空，支持匿名查询）',
    query_text TEXT NOT NULL COMMENT '查询问题',
    answer_text TEXT NOT NULL COMMENT '回答内容',
    retrieved_docs JSON COMMENT '检索到的文档片段',
    metadata JSON COMMENT '相关元数据',
    model_name VARCHAR(100) COMMENT '使用的AI模型',
    response_time_ms INT COMMENT '响应时间（毫秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_query_text (query_text)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问答历史表';
```

## 3. ChromaDB向量数据库设计

### 3.1 集合结构设计

**主要集合：**
- `knowledge_base`: 存储教学文档的向量表示

**集合配置：**
```python
collection_config = {
    "name": "knowledge_base",
    "metadata": {
        "description": "教学知识库向量存储",
        "hnsw:space": "cosine",  # 使用余弦相似度
        "hnsw:construction_ef": 200,
        "hnsw:M": 16
    },
    "embedding_function": "text-embedding-ada-002"
}
```

### 3.2 文档向量存储格式

**存储结构：**
```python
{
    "ids": ["doc_1_chunk_0", "doc_1_chunk_1", ...],
    "embeddings": [
        [0.1, 0.2, 0.3, ...],  # 1536维向量
        [0.4, 0.5, 0.6, ...],
        ...
    ],
    "metadatas": [
        {
            "source": "操作系统教程.pdf",
            "page": 1,
            "chunk_index": 0,
            "knowledge_point_id": 1,
            "document_id": "doc_1",
            "chunk_size": 500,
            "overlap": 50
        },
        ...
    ],
    "documents": [
        "操作系统是管理计算机硬件与软件资源的计算机程序...",
        "进程是程序的一次执行实例，具有独立的内存空间...",
        ...
    ]
}
```

**元数据字段说明：**
- `source`: 原始文档名称
- `page`: 页码（PDF文档）
- `chunk_index`: 文档块索引
- `knowledge_point_id`: 关联的知识点ID
- `document_id`: 文档唯一标识
- `chunk_size`: 文档块大小
- `overlap`: 重叠字符数

### 3.3 向量检索优化

**索引配置：**
```python
# HNSW索引参数
hnsw_config = {
    "space": "cosine",           # 距离度量
    "ef_construction": 200,      # 构建时的候选数量
    "M": 16,                     # 每个节点的最大连接数
    "max_elements": 1000000,     # 最大元素数量
    "ef": 100                    # 查询时的候选数量
}
```

**查询优化：**
- 使用余弦相似度进行语义匹配
- 支持元数据过滤查询
- 实现增量更新和删除
- 批量查询优化

## 4. 数据库优化策略

### 4.1 索引设计

**主要索引：**
```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role_status ON users(role, status);

-- 题目表索引
CREATE INDEX idx_questions_type_difficulty ON questions(question_type, difficulty_level);
CREATE INDEX idx_questions_knowledge_points ON questions((CAST(knowledge_point_ids AS CHAR(255) ARRAY)));

-- 练习记录表索引
CREATE INDEX idx_practice_user_time ON student_practice_records(user_id, start_time);
CREATE INDEX idx_practice_status_time ON student_practice_records(status, created_at);

-- 答题记录表索引
CREATE INDEX idx_answers_practice_question ON student_answers(practice_record_id, question_id);
CREATE INDEX idx_answers_score_time ON student_answers(ai_score, answer_submit_time);
```

### 4.2 查询优化

**分页查询优化：**
```sql
-- 使用LIMIT和OFFSET进行分页
SELECT * FROM student_practice_records 
WHERE user_id = ? 
ORDER BY created_at DESC 
LIMIT 20 OFFSET 0;

-- 使用游标分页（性能更好）
SELECT * FROM student_practice_records 
WHERE user_id = ? AND id < ? 
ORDER BY id DESC 
LIMIT 20;
```

**JSON字段查询优化：**
```sql
-- 查询包含特定知识点的题目
SELECT * FROM questions 
WHERE JSON_CONTAINS(knowledge_point_ids, '1');

-- 创建虚拟列索引
ALTER TABLE questions 
ADD COLUMN knowledge_point_list JSON AS (knowledge_point_ids) STORED;
CREATE INDEX idx_knowledge_point_list ON questions(knowledge_point_list);
```

### 4.3 性能监控

**慢查询监控：**
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;

-- 查看慢查询
SELECT * FROM mysql.slow_log 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 DAY);
```

**连接池配置：**
```python
# SQLAlchemy连接池配置
engine = create_engine(
    DATABASE_URL,
    pool_size=10,           # 连接池大小
    max_overflow=20,        # 最大溢出连接数
    pool_recycle=3600,      # 连接回收时间
    pool_pre_ping=True      # 连接前检查
)
```

## 5. 数据安全与备份

### 5.1 数据加密

**密码加密：**
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 密码哈希
hashed_password = pwd_context.hash("plain_password")

# 密码验证
is_valid = pwd_context.verify("plain_password", hashed_password)
```

**敏感数据加密：**
```sql
-- 使用AES加密存储敏感信息
INSERT INTO users (email, phone) VALUES 
(AES_ENCRYPT('<EMAIL>', 'encryption_key'),
 AES_ENCRYPT('13800138000', 'encryption_key'));

-- 解密查询
SELECT AES_DECRYPT(email, 'encryption_key') as email,
       AES_DECRYPT(phone, 'encryption_key') as phone
FROM users WHERE id = 1;
```

### 5.2 备份策略

**MySQL备份：**
```bash
# 全量备份
mysqldump -u root -p --single-transaction --routines --triggers teaching_agent > backup_$(date +%Y%m%d).sql

# 增量备份（启用binlog）
mysqlbinlog --start-datetime="2024-08-30 00:00:00" --stop-datetime="2024-08-30 23:59:59" mysql-bin.000001 > incremental_backup.sql
```

**ChromaDB备份：**
```python
# 导出向量数据
collection = client.get_collection("knowledge_base")
data = collection.get(include=["embeddings", "metadatas", "documents"])

# 保存到文件
import pickle
with open("chroma_backup.pkl", "wb") as f:
    pickle.dump(data, f)
```

## 6. 数据字典

### 6.1 枚举值定义

**用户角色 (UserRole)：**
- `student`: 学生
- `teacher`: 教师  
- `admin`: 管理员

**用户状态 (UserStatus)：**
- `active`: 激活
- `inactive`: 未激活
- `suspended`: 暂停

**题目类型 (QuestionType)：**
- `multiple_choice`: 选择题
- `fill_in_the_blank`: 填空题
- `short_answer`: 简答题

**难度级别 (DifficultyLevel)：**
- `easy`: 简单
- `medium`: 中等
- `hard`: 困难

**练习状态 (PracticeStatus)：**
- `in_progress`: 进行中
- `completed`: 已完成
- `abandoned`: 已放弃

### 6.2 约束规则

**数据完整性约束：**
- 用户名和邮箱必须唯一
- 密码长度至少8位
- 题目必须关联至少一个知识点
- 练习记录必须关联有效用户
- 答题记录必须关联有效的练习和题目

**业务逻辑约束：**
- 学生只能查看自己的练习记录
- 教师可以查看所教班级的学生记录
- 管理员可以查看所有记录
- 题目创建者可以编辑自己创建的题目

## 7. 数据库维护

### 7.1 定期维护任务

**表优化：**
```sql
-- 分析表统计信息
ANALYZE TABLE users, questions, student_practice_records;

-- 优化表结构
OPTIMIZE TABLE student_answers;

-- 检查表完整性
CHECK TABLE knowledge_points;
```

**清理过期数据：**
```sql
-- 清理30天前的问答历史
DELETE FROM qa_history 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理已删除的练习记录
DELETE FROM student_practice_records 
WHERE is_deleted = TRUE AND updated_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

### 7.2 监控指标

**关键监控指标：**
- 数据库连接数
- 查询响应时间
- 慢查询数量
- 表空间使用率
- 索引命中率
- 锁等待时间

**告警阈值：**
- 连接数 > 80%
- 平均查询时间 > 1秒
- 慢查询 > 10/分钟
- 表空间使用率 > 85%

---

**数据库设计总结：**

本数据库设计采用混合存储架构，充分发挥了关系型数据库和向量数据库的各自优势。通过合理的表结构设计、索引优化和查询策略，确保了系统的高性能和可扩展性。同时，完善的安全机制和备份策略保障了数据的安全性和可靠性。
