# 附录B：API接口文档

## 1. 接口概述

### 1.1 基础信息

- **API基础URL：** `http://localhost:8000`
- **API版本：** v0.1.0
- **数据格式：** JSON
- **字符编码：** UTF-8
- **认证方式：** JWT Bearer Token

### 1.2 通用响应格式

**成功响应：**
```json
{
  "status": "success",
  "data": {},
  "message": "操作成功"
}
```

**错误响应：**
```json
{
  "status": "error",
  "error_code": "ERROR_CODE",
  "message": "错误描述",
  "details": {}
}
```

### 1.3 HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 2. 认证授权API

### 2.1 用户注册

**接口地址：** `POST /api/auth/register`

**请求参数：**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "full_name": "string",
  "role": "student|teacher|admin"
}
```

**响应示例：**
```json
{
  "id": 1,
  "username": "student001",
  "email": "<EMAIL>",
  "full_name": "张三",
  "role": "student",
  "status": "active",
  "created_at": "2024-08-30T10:00:00Z"
}
```

### 2.2 用户登录

**接口地址：** `POST /api/auth/login`

**请求参数：**
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": 1,
    "username": "student001",
    "role": "student"
  }
}
```

### 2.3 获取当前用户信息

**接口地址：** `GET /api/auth/me`

**请求头：**
```
Authorization: Bearer <access_token>
```

**响应示例：**
```json
{
  "id": 1,
  "username": "student001",
  "email": "<EMAIL>",
  "full_name": "张三",
  "role": "student",
  "status": "active",
  "last_login_at": "2024-08-30T10:00:00Z"
}
```

## 3. 智能问答API

### 3.1 RAG问答

**接口地址：** `POST /api/rag/ask`

**请求参数：**
```json
{
  "query": "什么是操作系统？",
  "n_retrieved_docs": 3
}
```

**响应示例：**
```json
{
  "answer": "操作系统是管理计算机硬件与软件资源的计算机程序...",
  "retrieved_docs": [
    "操作系统概述：操作系统是一种系统软件...",
    "操作系统的主要功能包括进程管理、内存管理...",
    "现代操作系统通常采用分层架构设计..."
  ],
  "metadata": [
    {
      "source": "操作系统教程.pdf",
      "page": 1,
      "chunk_index": 0
    }
  ],
  "model_info": {
    "model_name": "gemini-2.5-flash-preview-05-20",
    "tokens_used": 150
  }
}
```

### 3.2 流式问答

**接口地址：** `POST /api/rag/ask_stream`

**请求参数：**
```json
{
  "query": "解释进程调度算法",
  "n_retrieved_docs": 5
}
```

**响应格式：** Server-Sent Events (SSE)
```
data: {"type": "start", "message": "开始生成回答"}

data: {"type": "content", "content": "进程调度"}

data: {"type": "content", "content": "算法是操作系统"}

data: {"type": "end", "metadata": {...}}
```

## 4. 知识点管理API

### 4.1 获取知识点层级结构

**接口地址：** `GET /api/knowledge_points/hierarchy`

**响应示例：**
```json
[
  {
    "id": 1,
    "name": "操作系统概述",
    "description": "操作系统的基本概念、功能和发展历史",
    "level": 1,
    "children": [
      {
        "id": 6,
        "name": "操作系统的定义和作用",
        "description": "什么是操作系统，操作系统的主要作用和功能",
        "level": 2,
        "children": []
      }
    ]
  }
]
```

### 4.2 创建知识点

**接口地址：** `POST /api/knowledge_points`

**权限要求：** 教师或管理员

**请求参数：**
```json
{
  "name": "进程同步",
  "description": "进程间的同步机制和方法",
  "parent_id": 2,
  "level": 2,
  "order_index": 3
}
```

**响应示例：**
```json
{
  "id": 15,
  "name": "进程同步",
  "description": "进程间的同步机制和方法",
  "parent_id": 2,
  "level": 2,
  "order_index": 3,
  "created_at": "2024-08-30T10:00:00Z"
}
```

## 5. 学生练习API

### 5.1 开始练习

**接口地址：** `POST /api/student/practice/start`

**权限要求：** 学生、教师或管理员

**请求参数：**
```json
{
  "practice_type": "knowledge_point",
  "knowledge_point_ids": [1, 2, 3],
  "question_count": 10,
  "difficulty_level": "medium"
}
```

**响应示例：**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "practice_record_id": 123,
  "questions": [
    {
      "id": 1,
      "question_text": "操作系统的主要功能包括？",
      "question_type": "multiple_choice",
      "options": ["A. 进程管理", "B. 内存管理", "C. 文件管理", "D. 以上都是"],
      "difficulty_level": "medium"
    }
  ],
  "total_questions": 10,
  "start_time": "2024-08-30T10:00:00Z"
}
```

### 5.2 提交答案

**接口地址：** `POST /api/student/practice/{session_id}/submit_answer`

**请求参数：**
```json
{
  "question_id": 1,
  "student_answer": "D",
  "answer_start_time": "2024-08-30T10:00:00Z",
  "answer_submit_time": "2024-08-30T10:01:30Z"
}
```

**响应示例：**
```json
{
  "answer_id": 456,
  "is_correct": true,
  "ai_score": null,
  "ai_feedback": null,
  "auto_graded": true,
  "grading_time": "2024-08-30T10:01:31Z",
  "correct_answer": "D",
  "explanation": "操作系统的主要功能确实包括进程管理、内存管理和文件管理。"
}
```

### 5.3 获取练习状态

**接口地址：** `GET /api/student/practice/{session_id}/status`

**响应示例：**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "in_progress",
  "total_questions": 10,
  "completed_questions": 5,
  "correct_count": 4,
  "current_score": 80.0,
  "start_time": "2024-08-30T10:00:00Z",
  "duration_seconds": 300
}
```

## 6. 题目生成API

### 6.1 生成题目

**接口地址：** `POST /api/teacher/questions/generate`

**权限要求：** 教师或管理员

**请求参数：**
```json
{
  "knowledge_point_ids": [1, 2],
  "question_types": ["multiple_choice", "short_answer"],
  "difficulty_level": "medium",
  "question_count": 5
}
```

**响应示例：**
```json
{
  "generated_questions": [
    {
      "id_temp": "temp_q_uuid_1",
      "question_text": "请解释操作系统中进程和线程的区别。",
      "question_type": "short_answer",
      "answer": "进程是程序的一次执行实例，线程是进程内的执行单元...",
      "explanation": "这道题考查学生对进程和线程概念的理解。",
      "difficulty_level": "medium",
      "knowledge_point_ids": [1, 2]
    }
  ],
  "generation_time": 2.5,
  "model_used": "gemini-2.5-flash-preview-05-20"
}
```

### 6.2 保存生成的题目

**接口地址：** `POST /api/teacher/questions/save`

**请求参数：**
```json
{
  "questions": [
    {
      "question_text": "操作系统的主要功能包括？",
      "question_type": "multiple_choice",
      "options": ["A. 进程管理", "B. 内存管理", "C. 文件管理", "D. 以上都是"],
      "answer": "D",
      "explanation": "操作系统的主要功能包括进程管理、内存管理、文件管理等。",
      "knowledge_point_ids": [1],
      "difficulty_level": "medium"
    }
  ]
}
```

## 7. 学习分析API

### 7.1 获取学生学习分析

**接口地址：** `GET /api/analytics/student/{user_id}`

**权限要求：** 学生本人、教师或管理员

**查询参数：**
- `days_back`: 分析天数（默认30天）

**响应示例：**
```json
{
  "overall_performance": {
    "total_practices": 25,
    "average_score": 78.5,
    "total_time_minutes": 450,
    "improvement_rate": 12.3
  },
  "knowledge_point_mastery": [
    {
      "knowledge_point_id": 1,
      "knowledge_point_name": "操作系统概述",
      "mastery_level": 85.2,
      "accuracy_rate": 0.82,
      "practice_count": 8
    }
  ],
  "weakness_areas": [
    {
      "knowledge_point_id": 3,
      "knowledge_point_name": "内存管理",
      "accuracy_rate": 0.45,
      "weakness_level": "high",
      "suggested_review": "建议重点复习虚拟内存和分页机制"
    }
  ]
}
```

### 7.2 获取系统使用统计

**接口地址：** `GET /api/analytics/usage`

**权限要求：** 管理员

**查询参数：**
- `days`: 统计天数（默认7天）

**响应示例：**
```json
{
  "daily_stats": [
    {
      "date": "2024-08-30",
      "active_users": 45,
      "teacher_active": 8,
      "student_active": 37,
      "practice_count": 120,
      "questions_generated": 25
    }
  ],
  "active_modules": [
    {
      "name": "智能问答",
      "daily_usage": 85,
      "weekly_usage": 78,
      "trend": "up"
    }
  ]
}
```

## 8. 教师资料管理API

### 8.1 上传教学文档

**接口地址：** `POST /api/teacher/materials/upload`

**权限要求：** 教师或管理员

**请求格式：** multipart/form-data

**请求参数：**
- `file`: 文档文件（PDF、DOCX等）
- `title`: 文档标题
- `description`: 文档描述
- `knowledge_point_ids`: 关联知识点ID列表

**响应示例：**
```json
{
  "material_id": 789,
  "title": "操作系统原理教程",
  "file_path": "/uploads/materials/os_tutorial.pdf",
  "file_size": 2048576,
  "upload_time": "2024-08-30T10:00:00Z",
  "processing_status": "processing"
}
```

### 8.2 生成PPT

**接口地址：** `POST /api/teacher/materials/generate_ppt`

**请求参数：**
```json
{
  "title": "操作系统概述",
  "knowledge_point_ids": [1, 2],
  "template_style": "academic",
  "slide_count": 15,
  "include_examples": true
}
```

**响应示例：**
```json
{
  "task_id": "ppt_task_uuid",
  "status": "processing",
  "estimated_time": 120,
  "created_at": "2024-08-30T10:00:00Z"
}
```

## 9. 管理员API

### 9.1 用户管理

**接口地址：** `GET /api/admin/users`

**权限要求：** 管理员

**查询参数：**
- `page`: 页码（默认1）
- `page_size`: 每页数量（默认20）
- `search`: 搜索关键词
- `role`: 角色筛选
- `status`: 状态筛选

**响应示例：**
```json
{
  "users": [
    {
      "id": 1,
      "username": "student001",
      "email": "<EMAIL>",
      "role": "student",
      "status": "active",
      "created_at": "2024-08-30T10:00:00Z",
      "last_login_at": "2024-08-30T09:30:00Z"
    }
  ],
  "total_count": 150,
  "total_pages": 8,
  "current_page": 1
}
```

### 9.2 系统统计

**接口地址：** `GET /api/admin/dashboard/stats`

**响应示例：**
```json
{
  "total_users": 150,
  "active_users_today": 45,
  "total_practices_today": 120,
  "total_practices_week": 850,
  "total_questions": 1250,
  "avg_score_today": 76.8,
  "popular_knowledge_points": [
    {
      "id": 1,
      "name": "操作系统概述",
      "practice_count": 85,
      "avg_score": 78.5
    }
  ]
}
```

## 10. 错误码说明

| 错误码 | 说明 |
|--------|------|
| AUTH_001 | 用户名或密码错误 |
| AUTH_002 | 令牌已过期 |
| AUTH_003 | 权限不足 |
| PARAM_001 | 必需参数缺失 |
| PARAM_002 | 参数格式错误 |
| RESOURCE_001 | 资源不存在 |
| RESOURCE_002 | 资源已存在 |
| SYSTEM_001 | 数据库连接错误 |
| SYSTEM_002 | 外部服务调用失败 |
| BUSINESS_001 | 业务逻辑错误 |

## 11. 接口调用示例

### 11.1 JavaScript/Axios示例

```javascript
// 设置基础配置
const api = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 10000
});

// 添加认证拦截器
api.interceptors.request.use(config => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 登录示例
const login = async (username, password) => {
  try {
    const response = await api.post('/api/auth/login', {
      username,
      password
    });
    localStorage.setItem('access_token', response.data.access_token);
    return response.data;
  } catch (error) {
    console.error('登录失败:', error.response.data);
    throw error;
  }
};

// 开始练习示例
const startPractice = async (knowledgePointIds) => {
  try {
    const response = await api.post('/api/student/practice/start', {
      practice_type: 'knowledge_point',
      knowledge_point_ids: knowledgePointIds,
      question_count: 10,
      difficulty_level: 'medium'
    });
    return response.data;
  } catch (error) {
    console.error('开始练习失败:', error.response.data);
    throw error;
  }
};
```

### 11.2 Python/Requests示例

```python
import requests
import json

class TeachingAgentAPI:
    def __init__(self, base_url='http://localhost:8000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None
    
    def login(self, username, password):
        """用户登录"""
        response = self.session.post(
            f'{self.base_url}/api/auth/login',
            json={'username': username, 'password': password}
        )
        if response.status_code == 200:
            data = response.json()
            self.token = data['access_token']
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}'
            })
            return data
        else:
            raise Exception(f'登录失败: {response.text}')
    
    def ask_question(self, query):
        """RAG问答"""
        response = self.session.post(
            f'{self.base_url}/api/rag/ask',
            json={'query': query, 'n_retrieved_docs': 3}
        )
        return response.json()

# 使用示例
api = TeachingAgentAPI()
api.login('student001', 'password123')
result = api.ask_question('什么是操作系统？')
print(result['answer'])
```

---

**注意事项：**

1. 所有需要认证的接口都必须在请求头中包含有效的JWT令牌
2. 令牌有效期为30分钟，过期后需要重新登录
3. 部分接口有权限限制，请确保当前用户具有相应权限
4. 文件上传接口需要使用multipart/form-data格式
5. 流式接口使用Server-Sent Events，需要特殊处理
6. 所有时间字段均使用ISO 8601格式（UTC时间）
